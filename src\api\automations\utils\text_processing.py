import re
from Levenshtein import distance, ratio

"""
    Funciones para procesar textos
        - Normalizar
        - Distancia de Levenshtein
        - Similaridad
        
    Referencias:
    https://rapidfuzz.github.io/Levenshtein/levenshtein.html#distance
"""


def levenshtein_distance(s1: str, s2: str) -> int:
    """
    Calcula la distancia de Levenshtein entre dos cadenas usando la librería
    python-Levenshtein
    """
    return distance(s1, s2)


def similarity_ratio(s1: str, s2: str) -> float:
    """
    Calcula la similitud entre dos cadenas usando la librería python-Levenshtein
    """
    return ratio(s1, s2)


def normalize_text(text: str, lowercase: bool = True) -> str:
    """
    Normaliza el texto removiendo acentos, espacios extra y convirtiendo a minúsculas
    """
    if not text:
        return ""

    # Remover espacios extra y convertir a minúsculas
    text = re.sub(r"\s+", " ", text.strip())

    if lowercase:
        text = text.lower()

    # Reemplazar caracteres especiales y acentos
    replacements = {
        "á": "a",
        "é": "e",
        "í": "i",
        "ó": "o",
        "ú": "u",
        "ü": "u",
        "ñ": "n",
    }
    for old, new in replacements.items():
        text = text.replace(old, new)

    return text
