#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile requirements.in
#
amqp==5.3.1
    # via kombu
argon2-cffi==23.1.0
    # via minio
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
asgiref==3.8.1
    # via
    #   django
    #   django-cors-headers
billiard==4.2.1
    # via celery
black==24.4.2
    # via -r requirements.in
cachetools==5.5.2
    # via google-auth
celery==5.4.0
    # via
    #   -r requirements.in
    #   django-celery-beat
    #   django-celery-results
certifi==2024.7.4
    # via
    #   minio
    #   requests
cffi==1.17.0
    # via argon2-cffi-bindings
charset-normalizer==3.4.0
    # via requests
click==8.1.7
    # via
    #   black
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
cron-descriptor==1.4.5
    # via django-celery-beat
django==5.0.6
    # via
    #   -r requirements.in
    #   django-celery-beat
    #   django-celery-results
    #   django-cors-headers
    #   django-enumchoicefield
    #   django-filter
    #   django-redis
    #   django-timezone-field
    #   djangorestframework
    #   drf-yasg
django-celery-beat==2.7.0
    # via -r requirements.in
django-celery-results==2.5.1
    # via -r requirements.in
django-cors-headers==4.3.1
    # via -r requirements.in
django-enumchoicefield==3.0.1
    # via -r requirements.in
django-filter==24.2
    # via -r requirements.in
django-json-widget==2.0.1
    # via -r requirements.in
django-redis==6.0.0
    # via -r requirements.in
django-timezone-field==7.1
    # via django-celery-beat
djangorestframework==3.15.1
    # via
    #   -r requirements.in
    #   drf-yasg
drf-yasg==1.21.7
    # via -r requirements.in
google-api-core==2.24.2
    # via google-api-python-client
google-api-python-client==2.165.0
    # via -r requirements.in
google-auth==2.38.0
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-auth-oauthlib
google-auth-httplib2==0.2.0
    # via
    #   -r requirements.in
    #   google-api-python-client
google-auth-oauthlib==1.2.1
    # via -r requirements.in
googleapis-common-protos==1.69.2
    # via google-api-core
gunicorn==23.0.0
    # via -r requirements.in
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
idna==3.10
    # via requests
inflection==0.5.1
    # via drf-yasg
kombu==5.4.2
    # via celery
levenshtein==0.27.1
    # via python-levenshtein
markdown==3.6
    # via -r requirements.in
mercadopago==2.2.3
    # via -r requirements.in
minio==7.2.7
    # via -r requirements.in
mypy-extensions==1.0.0
    # via black
oauthlib==3.2.2
    # via requests-oauthlib
packaging==24.0
    # via
    #   black
    #   drf-yasg
    #   gunicorn
pathspec==0.12.1
    # via black
phonenumbers==9.0.8
    # via -r requirements.in
pillow==10.4.0
    # via -r requirements.in
platformdirs==4.2.2
    # via black
prompt-toolkit==3.0.48
    # via click-repl
proto-plus==1.26.1
    # via google-api-core
protobuf==6.30.1
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   proto-plus
psycopg2-binary==2.9.9
    # via -r requirements.in
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.1
    # via google-auth
pycparser==2.22
    # via cffi
pycryptodome==3.20.0
    # via minio
pymupdf==1.26.4
    # via -r requirements.in
pyparsing==3.2.1
    # via httplib2
python-crontab==3.2.0
    # via django-celery-beat
python-dateutil==2.9.0.post0
    # via
    #   celery
    #   python-crontab
python-levenshtein==0.27.1
    # via -r requirements.in
pytz==2024.1
    # via drf-yasg
pyyaml==6.0.1
    # via drf-yasg
qrcode==8.2
    # via -r requirements.in
rapidfuzz==3.14.0
    # via levenshtein
redis==5.2.1
    # via
    #   -r requirements.in
    #   django-redis
requests==2.32.3
    # via
    #   google-api-core
    #   mercadopago
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via google-auth-oauthlib
rsa==4.9
    # via google-auth
shortuuid==1.0.13
    # via -r requirements.in
six==1.17.0
    # via python-dateutil
sqlparse==0.5.0
    # via django
typing-extensions==4.12.2
    # via minio
tzdata==2024.2
    # via
    #   celery
    #   django-celery-beat
    #   kombu
uritemplate==4.1.1
    # via
    #   drf-yasg
    #   google-api-python-client
urllib3==2.2.2
    # via
    #   minio
    #   requests
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
watchdog==6.0.0
    # via -r requirements.in
wcwidth==0.2.13
    # via prompt-toolkit
whitenoise==6.7.0
    # via -r requirements.in
