from rest_framework import routers
from api.lms.views.enrollment import LmsEnrollmentViewSet
from api.lms.views.credential import LmsCredentialViewSet

router = routers.DefaultRouter(trailing_slash=False)

router.register(
    r"enrollments",
    LmsEnrollmentViewSet,
    basename="lms-enrollment",
)

router.register(
    r"credentials",
    LmsCredentialViewSet,
    basename="lms-credential",
)


urlpatterns = router.urls
