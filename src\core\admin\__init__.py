from .blog import BlogAdmin
from .user import UserAdmin, TermAdmin, MajorAdmin, PermissionAdmin
from .instructor import InstructorAdmin
from .blog_category import BlogCategoryAdmin
from .testimonial import TestimonialAdmin
from .file import FileAdmin
from .offering import OfferingAdmin
from .enrollment import StudentEnrollmentAdmin
from .order import (
    OrderAdmin,
    OrderItemAdmin,
    ContactChannelAdmin,
    LeadOriginAdmin,
)
from .blog_tag import BlogTagAdmin
from .educational_institution import EducationalInstitutionAdmin, PartnershipAdmin
from .payment import PaymentAdmin
from .event import EventAdmin, EventScheduleAdmin
from .event_reminder import EventReminderAdmin
from .template import TemplateAdmin
from .credential import CredentialAdmin


__all__ = [
    "UserAdmin",
    "TermAdmin",
    "MajorAdmin",
    "InstructorAdmin",
    "TestimonialAdmin",
    "FileAdmin",
    "OfferingAdmin",
    "OrderAdmin",
    "OrderItemAdmin",
    "StudentEnrollmentAdmin",
    "ContactChannelAdmin",
    "LeadOriginAdmin",
    "BlogAdmin",
    "BlogCategoryAdmin",
    "BlogTagAdmin",
    "EducationalInstitutionAdmin",
    "PaymentAdmin",
    "EventAdmin",
    "EventScheduleAdmin",
    "PartnershipAdmin",
    "EventReminderAdmin",
    "TemplateAdmin",
    "PermissionAdmin",
    "CredentialAdmin",
]
