"""
Evolution API Integration
"""

from .execute.operations import EvolutionAPIClient
from .evolution_request import EvolutionAPIError

import logging
from django.conf import settings

from core.models.template import Template
from api.crm.utils.template import TemplateParser
from api.crm.utils.phone_numbers import PhoneNumberUtils
import random

__all__ = ["EvolutionAPIClient", "EvolutionAPIError"]

logger = logging.getLogger(__name__)


class EvolutionAPIService:
    """
    Evolution API integration for WhatsApp messaging, this is
    a service layer for business logic implementation

    Use EvolutionAPIService for:
     - Bulk operations
     - Business rule enforcement
    """

    def __init__(self, instance_name: str = None):
        # Instance name for default (temporary using env variable)
        # TODO: Use instances from database
        self.instance_name = instance_name or getattr(
            settings, "EVOLUTION_API_INSTANCE_NAME", ""
        )
        self.client = EvolutionAPIClient(self.instance_name)
        self.parser = TemplateParser()
        self.phone_validator = PhoneNumberUtils()

    # Event Reminders

    def send_event_invitation_preview(self, phone_number: str, template: Template):
        """
        Send WhatsApp invitation preview for testing purposes

        Args:
            phone_number: Target phone number
            template: Template instance to use
        """
        try:
            # Validate if template has a body_text
            if not template.body_text or template.body_text.strip() == "":
                raise EvolutionAPIError("La plantilla no tiene un cuerpo de texto")

            # Validate phone number
            is_number_valid = self.phone_validator.validate_whatsapp_number(
                phone_number, self.client
            )

            if not is_number_valid:
                raise EvolutionAPIError(
                    f"El número {phone_number}, no es un número válido de WhatsApp"
                )

            # Validate template
            if not self.parser.validate_template(template.body_text, template.type):
                raise EvolutionAPIError("Invalid template")

            parsed_message = self.parser.preview_template(
                template_text=template.body_text,
                template_type=template.type,
            )

            response = None
            if template.header_image is not None:
                response = self.client.messages.send_image(
                    remote_jid=phone_number,
                    media=template.header_image.url,
                    caption=parsed_message,
                    mimetype="image/jpeg",
                    file_name=template.header_image.name,
                )
            else:
                response = self.client.messages.send_text(
                    remote_jid=phone_number,
                    message_text=parsed_message,
                )

            # 3. Restart instance to simulate session close
            self.client.instances.restart()
            return response

        except EvolutionAPIError as e:
            # Re-raise Evolution API errors
            print(e)
            raise EvolutionAPIError("Failed to send invitation:" + str(e))
        except Exception as e:
            # Handle any other unexpected errors
            logger.error(f"Unexpected error sending invitation: {str(e)}")
            print(e)
            raise EvolutionAPIError(f"Failed to send invitation: {str(e)}")

    def send_event_invitation_message(self, phone_number: str, enrollment_id: int):
        """
        Send WhatsApp invitation message for event enrollment

        Args:
            enrollment_id: ID of the EventScheduleEnrollment

        Raises:
            EvolutionAPIError: If enrollment not found, no phone number available,
                              or API operation fails
        """
        from core.models.event import EventScheduleEnrollment
        import time

        try:
            # Get enrollment with all necessary relations
            enrollment = EventScheduleEnrollment.objects.select_related(
                "user",
                "event_schedule",
                "event_schedule__whatsapp_template",
                "event_schedule__whatsapp_template__type",
                "event_schedule__whatsapp_template__header_image",
            ).get(id=enrollment_id)

        except EventScheduleEnrollment.DoesNotExist:
            logger.error(f"Enrollment with ID {enrollment_id} not found")
            raise EvolutionAPIError(f"Enrollment {enrollment_id} not found")

        try:
            # Validate template
            template: Template = enrollment.event_schedule.whatsapp_template
            if not template:
                logger.error(f"Event {enrollment.event_schedule} has no template")
                raise EvolutionAPIError("No template available for event")

            # Parse template with enrollment data
            parsed_message = self.parser.parse_template(
                template_text=template.body_text,
                context_object=enrollment,
                template_type=template.type,
            )

            logger.info(
                f"Sending invitation to {phone_number} for enrollment {enrollment_id}"
            )

            random_delay = random.uniform(3, 5)
            # 1. Set presence to show typing
            self.client.chat.send_presence(
                remote_jid=phone_number,
                presence="composing",
                delay=random_delay
                * 1000,  # delay random entre 3000 a 5000 ms de composing
            )

            # Espera para simular "escribiendo..." antes de enviar el mensaje
            time.sleep(random_delay)

            # 2. Send message (media with caption or text only)
            response = {}
            if template.header_image is not None:
                response = self.client.messages.send_image(
                    remote_jid=phone_number,
                    media=template.header_image.url,
                    caption=parsed_message,
                )
                logger.info(f"Image message sent to {phone_number}")
            else:
                response = self.client.messages.send_text(
                    remote_jid=phone_number,
                    message_text=parsed_message,
                )
                logger.info(f"Text message sent to {phone_number}")

            # 3. Restart instance to simulate session close
            self.client.instances.restart()
            return response

        except EvolutionAPIError as e:
            # Re-raise Evolution API errors
            raise EvolutionAPIError("Failed to send invitation:" + str(e))
        except Exception as e:
            # Handle any other unexpected errors
            logger.error(
                f"Unexpected error sending invitation for enrollment {enrollment_id}: {str(e)}"
            )
            raise EvolutionAPIError(f"Failed to send invitation: {str(e)}")
