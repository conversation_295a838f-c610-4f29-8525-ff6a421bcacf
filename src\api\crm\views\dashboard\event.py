"""
Event Dashboard Views for CRM
Provides analytics endpoints for event dashboard
"""

from django.db.models import Q, Count, Case, When, IntegerField
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser
from core.models import (
    Event,
    EventSchedule,
    EventScheduleEnrollment,
    Order,
    EventReminder,
    EducationalInstitution,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from services.cache.redis import CacheManager
from api.crm.filters.dashboard.event import CrmDashboardEventFilter
from api.crm.serializers.dashboard.event import (
    CrmDashboardEventSummarySerializer,
    CrmDashboardEventAnalyticsSerializer,
    CrmDashboardEventSegmentationSerializer,
    CrmDashboardEventLaunchedEventSerializer,
    CrmDashboardEventLaunchedSerializer,
    CrmDashboardEventHistogramSerializer,
)
from api.crm.utils.dashboard import DashboardUtils
from collections import defaultdict
from api.crm.utils.event_reminder import CrmEventReminderUtils
from django.utils import timezone


class CrmDashboardEventViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Event Dashboard Analytics
    Provides various endpoints for event dashboard statistics and charts
    """

    model_class = EventSchedule
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
    filterset_class = CrmDashboardEventFilter
    swagger_tags = ["CRM Dashboard"]
    serializer_class = CrmDashboardEventSummarySerializer

    def get_serializer_class(self):
        if self.action == "invalidate_cache":
            return None
        elif self.action == "analytics":
            return CrmDashboardEventAnalyticsSerializer
        elif self.action == "segmentation":
            return CrmDashboardEventSegmentationSerializer
        elif self.action == "launched":
            return CrmDashboardEventLaunchedSerializer
        elif self.action == "histogram":
            return CrmDashboardEventHistogramSerializer
        return super().get_serializer_class()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_manager = CacheManager("crm_dashboard_event")
        self.cache_timeout = 60 * 5  # 5 minutes

    def initial(self, request, *args, **kwargs):
        """
        Check for force_refresh parameter before any endpoint execution
        """
        super().initial(request, *args, **kwargs)

        # Force refresh cache if requested
        if request.GET.get("force_refresh", "").lower() == "true" and not getattr(
            self, "_cache_invalidated", False
        ):
            self.cache_manager.invalidate()
            self._cache_invalidated = True

    def get_queryset(self):
        """
        Get base queryset for event schedules (non-deleted schedules only)
        """
        return EventSchedule.objects.filter(deleted=False)

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            "start_date": self.request.GET.get("start_date", ""),
            "end_date": self.request.GET.get("end_date", ""),
            "stage": self.request.GET.get("stage", ""),
            "event_type": self.request.GET.get("event_type", ""),
            "modality": self.request.GET.get("modality", ""),
            "programs": self.request.GET.get("programs", ""),
            "events": self.request.GET.get("events", ""),
        }

    # ==== Utilities ====

    def _get_report_dates(self):
        """Get current and previous period dates"""
        return DashboardUtils.get_report_dates(self.request)

    def _get_queryset_excluding_filters(self, exclude_fields):
        """Get queryset excluding specific filters"""
        return DashboardUtils.get_queryset_excluding_filters(
            self.get_queryset(), self.filterset_class, self.request, exclude_fields
        )

    # ==== CALCULATION FUNCTIONS ====

    def calculate_summary_stats(self):
        """
        Calculate general event dashboard statistics
        """
        filtered_queryset = self.get_filtered_queryset()

        # 1. Stats: number of created schedules by stage
        stage_stats = filtered_queryset.aggregate(
            planning_count=Count(
                "esid",
                filter=Q(stage=Event.PLANNING_STAGE),
            ),
            launched_count=Count(
                "esid",
                filter=Q(stage=Event.LAUNCHED_STAGE),
            ),
            enrollment_closed_count=Count(
                "esid",
                filter=Q(stage=Event.ENROLLMENT_CLOSED_STAGE),
            ),
            finished_count=Count(
                "esid",
                filter=Q(stage=Event.FINISHED_STAGE),
            ),
        )

        # 2. Needs conciliation: total registered users in events that require conciliation
        needs_conciliation_count = EventScheduleEnrollment.objects.filter(
            event_schedule__in=filtered_queryset,
            needs_conciliation=True,
            deleted=False,
        ).count()

        # 3. Alliances enrollments: number of registered users that come from alliances
        alliances_enrollments_count = EventScheduleEnrollment.objects.filter(
            event_schedule__in=filtered_queryset,
            partnership__isnull=False,
            deleted=False,
        ).count()

        # 4. Conversion: percentage of registered users that later bought a program
        conversion_rate = self._calculate_conversion_rate(filtered_queryset)

        return {
            "stats": {
                "planning": stage_stats["planning_count"],
                "launched": stage_stats["launched_count"],
                "enrollment_closed": stage_stats["enrollment_closed_count"],
                "finished": stage_stats["finished_count"],
                "total": sum(stage_stats.values()),
            },
            "needs_conciliation": needs_conciliation_count,
            "alliances_enrollments": alliances_enrollments_count,
            "conversion": conversion_rate,
        }

    def _calculate_conversion_rate(self, event_schedules_queryset):
        """
        Calculate conversion rate from event enrollments to program purchases
        """
        # Get all enrollments for the filtered event schedules
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules_queryset,
            deleted=False,
        ).select_related("event_schedule__event")

        if not enrollments.exists():
            return {"percentage": 0.0, "converted": 0, "total_enrollments": 0}

        total_enrollments = enrollments.count()
        converted_count = 0

        # For each enrollment, check if there's a matching sold order
        for enrollment in enrollments:
            if enrollment.email:
                # Check if user bought a program related to this event after event end date
                event_end_date = enrollment.event_schedule.end_date
                event_offering = enrollment.event_schedule.event.offering

                if event_offering:
                    # Look for orders where:
                    # 1. User email matches enrollment email
                    # 2. Order contains the same offering as the event
                    # 3. Order was sold after event end date
                    matching_order = Order.objects.filter(
                        owner__email=enrollment.email,
                        stage=Order.SOLD_STAGE,
                        sold_at__gte=event_end_date,
                        items__offering=event_offering,
                        deleted=False,
                    ).first()

                    if matching_order:
                        converted_count += 1

        conversion_percentage = (
            (converted_count / total_enrollments * 100) if total_enrollments > 0 else 0
        )

        return {
            "percentage": round(conversion_percentage, 2),
            "converted": converted_count,
            "total_enrollments": total_enrollments,
        }

    def calculate_analytics_stats(self):
        """
        Calculate analytics grouped by type and channels
        """
        filtered_queryset = self.get_filtered_queryset()

        # 1. Event by type: general vs specific events
        event_by_type = self._calculate_event_by_type(filtered_queryset)

        # 2. Diffusion channels: list of channels sorted by total enrollments
        diffusion_channels = self._calculate_diffusion_channels(filtered_queryset)

        # 3. Top alliances: alliances associated with events
        top_alliances = self._calculate_top_alliances(filtered_queryset)

        return {
            "event_by_type": event_by_type,
            "diffusion_channels": diffusion_channels,
            "top_alliances": top_alliances,
        }

    def _calculate_event_by_type(self, event_schedules_queryset):
        """
        Calculate events grouped by event type
        """
        # Get the event types and their counts using aggregation
        type_stats = event_schedules_queryset.aggregate(
            workshop_count=Count(
                "esid",
                filter=Q(event__type=Event.WORKSHOP_TYPE),
            ),
            webinar_count=Count(
                "esid",
                filter=Q(event__type=Event.WEBINAR_TYPE),
            ),
            hands_on_workshop_count=Count(
                "esid",
                filter=Q(event__type=Event.HANDS_OF_WORKSHOP_TYPE),
            ),
        )

        # Calculate enrollments for each event type using aggregation
        enrollment_stats = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules_queryset, deleted=False
        ).aggregate(
            workshop_enrollments=Count(
                "id",
                filter=Q(event_schedule__event__type=Event.WORKSHOP_TYPE),
            ),
            webinar_enrollments=Count(
                "id",
                filter=Q(event_schedule__event__type=Event.WEBINAR_TYPE),
            ),
            hands_on_workshop_enrollments=Count(
                "id",
                filter=Q(event_schedule__event__type=Event.HANDS_OF_WORKSHOP_TYPE),
            ),
        )

        return {
            Event.WORKSHOP_TYPE: {
                "count": type_stats["workshop_count"],
                "total_enrollments": enrollment_stats["workshop_enrollments"],
            },
            Event.WEBINAR_TYPE: {
                "count": type_stats["webinar_count"],
                "total_enrollments": enrollment_stats["webinar_enrollments"],
            },
            Event.HANDS_OF_WORKSHOP_TYPE: {
                "count": type_stats["hands_on_workshop_count"],
                "total_enrollments": enrollment_stats["hands_on_workshop_enrollments"],
            },
        }

    def _calculate_diffusion_channels(self, event_schedules_queryset):
        """
        Calculate diffusion channels sorted by total enrollments
        """
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules_queryset,
            deleted=False,
            diffusion_channel__isnull=False,
        ).exclude(diffusion_channel="")

        channel_stats = {}
        for enrollment in enrollments:
            channel = enrollment.diffusion_channel
            if channel not in channel_stats:
                channel_stats[channel] = {
                    "total": 0,
                    "has_contact": 0,
                    "needs_conciliation": 0,
                    "already_lead": 0,
                }

            channel_stats[channel]["total"] += 1
            if enrollment.has_contact:
                channel_stats[channel]["has_contact"] += 1
            if enrollment.needs_conciliation:
                channel_stats[channel]["needs_conciliation"] += 1
            if enrollment.already_lead:
                channel_stats[channel]["already_lead"] += 1

        # Convert to list and sort by total enrollments
        channels_list = [
            {
                "channel": channel,
                "total": stats["total"],
                "has_contact": stats["has_contact"],
                "needs_conciliation": stats["needs_conciliation"],
                "already_lead": stats["already_lead"],
            }
            for channel, stats in channel_stats.items()
        ]

        # Sort by total enrollments descending
        channels_list.sort(key=lambda x: x["total"], reverse=True)

        return channels_list

    def _calculate_top_alliances(self, event_schedules_queryset):
        """
        Calculate top alliances associated with events
        """
        # Get enrollments with partnerships
        enrollments_with_partnerships = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules_queryset,
            partnership__isnull=False,
            deleted=False,
        ).select_related("partnership")

        enrollments_with_partnerships_count = enrollments_with_partnerships.count()

        # Group by partnership and calculate metrics
        alliance_stats = {}
        total_global_enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules_queryset, deleted=False
        ).count()

        for enrollment in enrollments_with_partnerships:
            partnership = enrollment.partnership
            if partnership.pid not in alliance_stats:
                alliance_stats[partnership.pid] = {
                    "name": partnership.name,
                    "associated_events": set(),
                    "unique_enrollments": set(),
                    "total_enrollments": 0,
                }

            alliance_stats[partnership.pid]["associated_events"].add(
                enrollment.event_schedule.esid
            )
            alliance_stats[partnership.pid]["total_enrollments"] += 1

            # Add to unique enrollments (by email/phone)
            unique_key = enrollment.email or enrollment.phone_number
            if unique_key:
                alliance_stats[partnership.pid]["unique_enrollments"].add(unique_key)

        # Convert to list and calculate percentages
        alliances_list = []
        for _, stats in alliance_stats.items():
            unique_count = len(stats["unique_enrollments"])
            total_count = stats["total_enrollments"]
            # Global participation percentage
            global_participation_percentage = (
                (total_count / total_global_enrollments * 100)
                if total_global_enrollments > 0
                else 0
            )

            # Participation percentage of enrollments with partnerships
            participation_percentage = (
                (total_count / enrollments_with_partnerships_count * 100)
                if enrollments_with_partnerships_count > 0
                else 0
            )

            alliances_list.append(
                {
                    "alliance_name": stats["name"],
                    "associated_events_count": len(stats["associated_events"]),
                    "unique_enrollments": unique_count,
                    "total_enrollments": total_count,
                    "global_participation_percentage": round(
                        global_participation_percentage, 2
                    ),
                    "participation_percentage": round(participation_percentage, 2),
                }
            )

        # Sort by participation percentage descending
        alliances_list.sort(key=lambda x: x["participation_percentage"], reverse=True)

        return alliances_list

    def calculate_segmentation_stats(self):
        """
        Calculate segmentation of users registered in events
        """
        filtered_queryset = self.get_filtered_queryset()

        # 1. Interests: count of interests grouped by specialization
        interests_breakdown = self._calculate_interests_breakdown(filtered_queryset)

        # 2. Contacts: segmentation between new_contacts and has_contact
        contacts_segmentation = self._calculate_contacts_segmentation(filtered_queryset)

        return {
            "interests": interests_breakdown,
            "contacts": contacts_segmentation,
        }

    def _calculate_interests_breakdown(self, event_schedules_queryset):
        """
        Calculate interests breakdown from enrollments JSONField
        Calcular desglose de intereses del campo JSON de inscripciones
        """
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules_queryset,
            deleted=False,
            interests__isnull=False,
        ).exclude(interests=[])

        interests_count = defaultdict(int)

        # Process each enrollment's interests
        for enrollment in enrollments:
            if enrollment.interests and isinstance(enrollment.interests, list):
                for interest in enrollment.interests:
                    if isinstance(interest, str):
                        interests_count[interest] += 1
                    elif isinstance(interest, dict) and "specialization" in interest:
                        # If interests are stored as objects with specialization field
                        interests_count[interest["specialization"]] += 1

        # Convert to list and sort descending
        interests_list = [
            {"specialization": interest, "count": count}
            for interest, count in interests_count.items()
        ]
        interests_list.sort(key=lambda x: x["count"], reverse=True)

        return interests_list

    def _calculate_contacts_segmentation(self, event_schedules_queryset):
        """
        Calculate contacts segmentation with uniqueness applied
        """
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules_queryset, deleted=False
        )

        # Track unique users by email/phone to avoid counting same user multiple times
        unique_users = {}
        for enrollment in enrollments:
            unique_key = enrollment.email or enrollment.phone_number
            if unique_key:
                if unique_key not in unique_users:
                    unique_users[unique_key] = enrollment.has_contact
                # If user appears multiple times, prioritize has_contact=True
                elif enrollment.has_contact:
                    unique_users[unique_key] = True

        # Count new contacts vs has contact
        new_contacts = sum(
            1 for has_contact in unique_users.values() if not has_contact
        )
        has_contact = sum(1 for has_contact in unique_users.values() if has_contact)
        total = len(unique_users)

        new_contacts_percentage = (new_contacts / total * 100) if total > 0 else 0
        has_contact_percentage = (has_contact / total * 100) if total > 0 else 0

        return {
            "new_contacts": {
                "total": new_contacts,
                "percentage": round(new_contacts_percentage, 2),
            },
            "has_contact": {
                "total": has_contact,
                "percentage": round(has_contact_percentage, 2),
            },
        }

    def calculate_launched_events(self):
        """
        Calculate live metrics for events currently in LAUNCHED_STAGE
        """
        # Get only launched event schedules ordered by priority considering start_date, end_date and current date
        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)

        launched_schedules = (
            self._get_queryset_excluding_filters("created_at")
            .filter(stage=Event.LAUNCHED_STAGE)
            .select_related("event")
            .annotate(
                priority=Case(
                    # Prioridad 1: Eventos en curso
                    When(start_date__lte=now, end_date__gte=now, then=1),
                    # Prioridad 2: Eventos futuros (start_date > now)
                    When(start_date__gt=now, then=2),
                    # Prioridad 3: Eventos pasados (end_date < now)
                    When(end_date__lt=now, then=3),
                    default=3,
                    output_field=IntegerField(),
                )
            )
        ).order_by(
            "priority",
            "-start_date",
        )

        launched_events = []
        for schedule in launched_schedules:
            enrollment_count = EventScheduleEnrollment.objects.filter(
                event_schedule=schedule, deleted=False
            ).count()

            # Calculate invitation status (reminders)
            invitation_status = self._calculate_invitation_status(schedule)

            # Map priority to event status
            event_status = CrmDashboardEventLaunchedEventSerializer.IN_COURSE
            if schedule.priority == 2:
                event_status = CrmDashboardEventLaunchedEventSerializer.FUTURE
            elif schedule.priority == 3:
                event_status = CrmDashboardEventLaunchedEventSerializer.PAST

            launched_events.append(
                {
                    "esid": schedule.esid,
                    "event_name": schedule.name or schedule.event.name,
                    "start_date": schedule.start_date,
                    "end_date": schedule.end_date,
                    "ext_event_link": schedule.ext_event_link,
                    "enrollment_count": enrollment_count,
                    "invitation_status": invitation_status,
                    "event_status": event_status,
                    "offering": (
                        schedule.event.offering.long_name
                        if schedule.event.offering.long_name
                        else schedule.event.offering.name
                    ),
                }
            )

        return launched_events

    def _calculate_invitation_status(self, event_schedule):
        """
        Calculate invitation status for reminders (email and WhatsApp) using real EventReminder data
        """
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule=event_schedule, deleted=False
        )

        # Get all event reminders for this event schedule's enrollments
        event_reminders = EventReminder.objects.filter(
            enrollment__in=enrollments, deleted=False
        ).select_related("enrollment__event_schedule")

        metrics_data = CrmEventReminderUtils.calculate_metrics(event_reminders)

        return {
            "total_reminders": metrics_data["total_reminders"],
            "total_sent": metrics_data["total_sent"],
            "total_pending": metrics_data["total_pending"],
            "total_failed": metrics_data["total_failed"],
            "email": metrics_data["email"],
            "whatsapp": metrics_data["whatsapp"],
        }

    # ==== DASHBOARD ENDPOINTS ====

    @action(detail=False, methods=["GET"], url_path="summary")
    def summary(self, request):
        """
        Get event dashboard summary statistics
        """
        cache_key = f"summary_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_summary_stats()
            data["filters_applied"] = self.get_cache_key_params()

            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating event summary: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="analytics")
    def analytics(self, request):
        """
        Get event dashboard analytics (by type and channels)
        """
        cache_key = f"analytics_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_analytics_stats()
            data["filters_applied"] = self.get_cache_key_params()

            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating event analytics: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="segmentation")
    def segmentation(self, request):
        """
        Get event dashboard segmentation statistics
        """
        cache_key = f"segmentation_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_segmentation_stats()
            data["filters_applied"] = self.get_cache_key_params()

            # Cache the result
            self.cache_manager.set(cache_key, data, timeout=self.cache_timeout)

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating event segmentation: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="launched")
    def launched(self, request):
        """
        Get live metrics for events currently in LAUNCHED_STAGE
        """
        cache_key = f"launched_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_launched_events()
            self.cache_manager.set(cache_key, data, timeout=60)  # 1 minute

            return Response(data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating launched events: {str(e)}"}, status=500
            )

    def calculate_historical_data(self):
        """
        Calculate histogram data for event schedules with educational institution enrollments
        """
        # Get date filters if provided
        start_date = self.request.GET.get("start_date")
        end_date = self.request.GET.get("end_date")

        # Base queryset: exclude planning stage and use filtered queryset (which already handles date filtering)
        queryset = self.get_filtered_queryset().exclude(stage=Event.PLANNING_STAGE)

        if start_date or end_date:
            # If date filters are provided, the queryset is already filtered by the filterset
            # Order by start_date for consistent results
            event_schedules = queryset.order_by("start_date")
        else:
            # If no date filters, get last 10 event schedules ordered by start_date
            event_schedules = queryset.order_by("-start_date")[:10]
            # reordenar event schedules extraides por start_date
            event_schedules = sorted(event_schedules, key=lambda x: x.start_date)

        histogram_data = []

        for schedule in event_schedules:
            # Get all enrollments for this schedule
            enrollments = EventScheduleEnrollment.objects.filter(
                event_schedule=schedule, deleted=False
            ).select_related("partnership__institution")

            total_enrollments = enrollments.count()

            # Count enrollments by educational institution
            institution_counts = {}
            withouth_institution_count = 0

            for enrollment in enrollments:
                institution_name = None
                institution_acronym = None

                # First try to get institution from partnership
                if enrollment.partnership and enrollment.partnership.institution:
                    institution_name = enrollment.partnership.institution.name
                    institution_acronym = enrollment.partnership.institution.acronym
                # Otherwise, try to get from enrollment.university field
                elif enrollment.university:
                    # Try to find matching EducationalInstitution by name
                    try:
                        institution = EducationalInstitution.objects.get(
                            name=enrollment.university, deleted=False
                        )
                        institution_name = institution.name
                        institution_acronym = institution.acronym
                    except EducationalInstitution.DoesNotExist:
                        # If not found, use the university name directly
                        institution_name = enrollment.university
                        institution_acronym = None

                if institution_name:
                    key = (institution_name, institution_acronym)
                    if key not in institution_counts:
                        institution_counts[key] = 0
                    institution_counts[key] += 1
                else:
                    # Count enrollments without educational institution
                    withouth_institution_count += 1

            # Sort institutions by enrollment count and get top 5
            sorted_institutions = sorted(
                institution_counts.items(), key=lambda x: x[1], reverse=True
            )

            top_institutions = []
            others_count = 0

            for i, ((name, acronym), count) in enumerate(sorted_institutions):
                if i < 5:
                    top_institutions.append(
                        {"enrollments": count, "name": name, "acronym": acronym}
                    )
                else:
                    others_count += count

            # Add "Otros" if there are more than 5 institutions
            if others_count > 0:
                top_institutions.append(
                    {"enrollments": others_count, "name": "Otros", "acronym": None}
                )

            histogram_data.append(
                {
                    "esid": schedule.esid,
                    "event_name": schedule.name or schedule.event.name,
                    "start_date": schedule.start_date,
                    "end_date": schedule.end_date,
                    "total_enrollments": total_enrollments,
                    "withouth_educational_institution": withouth_institution_count,
                    "top_educational_institutions": top_institutions,
                }
            )

        return histogram_data

    @action(detail=False, methods=["GET"], url_path="historical")
    def historical(self, request):
        """
        Get histogram of event schedules with educational institution enrollment breakdown
        Shows enrollment distribution by educational institutions for each event schedule

        Query Parameters:
        - start_date: Filter schedules starting after this date
        - end_date: Filter schedules starting before this date
        - If no date filters provided, returns last 10 schedules (excluding planning stage)
        """
        cache_key = f"historical_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            data = self.calculate_historical_data()

            # Add filter information
            response_data = {
                "data": data,
                "filters_applied": self.get_cache_key_params(),
            }

            # Cache the result
            self.cache_manager.set(cache_key, response_data, timeout=self.cache_timeout)

            return Response(response_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating histogram data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["POST"], url_path="invalidate-cache")
    def invalidate_cache(self, request):
        """
        Invalidate all cached data for event dashboard
        """
        try:
            self.cache_manager.invalidate()
            return Response({"message": "Cache invalidated successfully"})
        except Exception as e:
            return Response(
                {"error": f"Error invalidating cache: {str(e)}"}, status=500
            )
